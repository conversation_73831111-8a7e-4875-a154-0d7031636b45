package cli

import (
	"fmt"

	"github.com/spf13/cobra"
	"github.com/berrijam/mulberri/internal/config"
)

// NewTrainCommand creates the train subcommand for building decision trees.
//
// Handles training-specific flags, validation, and delegates to training package.
// Uses defaults from config package and validates all required parameters.
//
// Returns cobra command configured with training flags and validation.
func NewTrainCommand() *cobra.Command {
	cfg := &TrainingConfig{
		MaxDepth:        config.DefaultMaxDepth,
		MinSamplesSplit: config.DefaultMinSamples,
		Criterion:       config.DefaultCriterion,
	}

	trainCmd := &cobra.Command{
		Use:   "train",
		Short: "Train a C4.5 decision tree model",
		Long: `Train a decision tree model from CSV data using the C4.5 algorithm.

The training process builds an interpretable tree structure optimized for
classification tasks with configurable depth and splitting criteria.`,
		Example: `  # Basic training
  mulberri train -i data.csv -t species -o model.dt

  # Training with custom parameters
  mulberri train -i data.csv -t target -o model.dt --max-depth 15 --verbose

  # Training with feature info file
  mulberri train -i data.csv -t target -o model.dt -f features.yaml`,
		RunE: func(cmd *cobra.Command, args []string) error {
			if err := cfg.Validate(); err != nil {
				return fmt.Errorf("validation failed: %w", err)
			}
			return runTraining(cfg)
		},
	}

	// Required flags
	trainCmd.Flags().StringVarP(&cfg.InputFile, "input", "i", "", "Input CSV file path")
	trainCmd.Flags().StringVarP(&cfg.TargetCol, "target", "t", "", "Target column name")
	trainCmd.Flags().StringVarP(&cfg.OutputFile, "output", "o", "", "Output model file path")

	// Optional flags with defaults
	trainCmd.Flags().StringVarP(&cfg.FeatureInfoFile, "features", "f", "", "Feature info YAML file path")
	trainCmd.Flags().IntVar(&cfg.MaxDepth, "max-depth", cfg.MaxDepth, "Maximum tree depth")
	trainCmd.Flags().IntVar(&cfg.MinSamplesSplit, "min-samples", cfg.MinSamplesSplit, "Minimum samples to split")
	trainCmd.Flags().StringVar(&cfg.Criterion, "criterion", cfg.Criterion, "Split criterion (entropy only)")
	trainCmd.Flags().BoolVarP(&cfg.Verbose, "verbose", "v", false, "Enable verbose output")

	// Mark required flags
	trainCmd.MarkFlagRequired("input")
	trainCmd.MarkFlagRequired("target")
	trainCmd.MarkFlagRequired("output")

	return trainCmd
}

// runTraining executes the training workflow with validated configuration.
//
// Args:
// - cfg: Validated training configuration
//
// Returns error if training fails, logs progress if verbose enabled.
func runTraining(cfg *TrainingConfig) error {
	// TODO: Delegate to internal/training package
	if cfg.Verbose {
		fmt.Printf("Training decision tree with max_depth=%d, min_samples=%d, criterion=%s\n", 
			cfg.MaxDepth, cfg.MinSamplesSplit, cfg.Criterion)
		if cfg.FeatureInfoFile != "" {
			fmt.Printf("Using feature info file: %s\n", cfg.FeatureInfoFile)
		}
	}
	
	fmt.Printf("Training: %s -> %s (target: %s)\n", 
		cfg.InputFile, cfg.OutputFile, cfg.TargetCol)
	
	// Example of how this would delegate to training package:
	// trainer := training.NewTrainer(cfg.MaxDepth, cfg.MinSamplesSplit, cfg.Criterion)
	// tree, err := trainer.Train(cfg.InputFile, cfg.TargetCol, cfg.FeatureInfoFile)
	// if err != nil {
	//     return fmt.Errorf("training failed: %w", err)
	// }
	// return tree.SaveToFile(cfg.OutputFile)
	
	return nil
}