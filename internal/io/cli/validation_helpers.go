package cli

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
)

// validateInputFile validates CSV input file existence and format.
//
// Args:
// - inputFile: Path to input CSV file
//
// Returns error if file doesn't exist, has wrong extension, or is directory.
func validateInputFile(inputFile string) error {
	if strings.TrimSpace(inputFile) == "" {
		return fmt.Errorf("input file path is required")
	}

	// Check file existence and properties first
	info, err := os.Stat(inputFile)
	if os.IsNotExist(err) {
		return fmt.Errorf("file does not exist: %s", inputFile)
	}
	if err != nil {
		return fmt.Errorf("cannot access file: %w", err)
	}

	if info.IsDir() {
		return fmt.Errorf("path is a directory, not a file: %s", inputFile)
	}

	// Validate file extension after confirming it's a file
	ext := strings.ToLower(filepath.Ext(inputFile))
	if ext != ".csv" {
		return fmt.Errorf("input file must be CSV format, got: %s", ext)
	}

	return nil
}

// validateModelFile validates trained model file existence and format.
//
// Args:
// - modelFile: Path to trained model file
//
// Returns error if file doesn't exist or has wrong extension (.dt required).
func validateModelFile(modelFile string) error {
	// Validate file extension
	ext := strings.ToLower(filepath.Ext(modelFile))
	if ext != ".dt" {
		return fmt.Errorf("model file must have .dt extension, got: %s", ext)
	}

	// Check file existence
	if _, err := os.Stat(modelFile); os.IsNotExist(err) {
		return fmt.Errorf("model file does not exist: %s", modelFile)
	}

	return nil
}

// validateYAMLFile validates YAML file existence and format.
//
// Args:
// - yamlFile: Path to YAML file
//
// Returns error if file doesn't exist or has wrong extension (.yaml/.yml required).
func validateYAMLFile(yamlFile string) error {
	// Validate file extension
	ext := strings.ToLower(filepath.Ext(yamlFile))
	if ext != ".yaml" && ext != ".yml" {
		return fmt.Errorf("file must be YAML format (.yaml or .yml), got: %s", ext)
	}

	// Check file existence
	if _, err := os.Stat(yamlFile); os.IsNotExist(err) {
		return fmt.Errorf("YAML file does not exist: %s", yamlFile)
	}

	return nil
}
