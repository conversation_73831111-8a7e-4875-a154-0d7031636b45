package cli

import (
	"fmt"
	"strings"
)

// TrainingConfig holds configuration specific to training operations.
//
// Contains all parameters needed for training decision trees including
// file paths, algorithm parameters, and feature specifications.
// Initialized with defaults from config package.
type TrainingConfig struct {
	// File paths
	InputFile       string // CSV file with training data
	TargetCol       string // Name of target column
	OutputFile      string // Path to save trained model
	FeatureInfoFile string // Optional feature metadata YAML file

	// Algorithm parameters
	MaxDepth        int    // Maximum tree depth
	MinSamplesSplit int    // Minimum samples to split node
	Criterion       string // Splitting criterion (entropy only)

	// Options
	Verbose bool // Enable detailed output
}

// PredictionConfig holds configuration specific to prediction operations.
//
// Contains parameters needed for making predictions with trained models
// including input data, model file, and output specifications.
type PredictionConfig struct {
	// File paths
	InputFile  string // CSV file with data to predict
	ModelFile  string // Trained model file (.dt format)
	OutputFile string // Path to save predictions

	// Options
	Verbose bool // Enable detailed output
}

// Validate performs comprehensive validation for training configuration.
//
// Checks file existence, parameter ranges, and required fields.
// Returns first validation error encountered for faster feedback.
func (c *TrainingConfig) Validate() error {
	if err := validateInputFile(c.InputFile); err != nil {
		return fmt.Errorf("input file validation: %w", err)
	}

	if strings.TrimSpace(c.TargetCol) == "" {
		return fmt.Errorf("target column is required")
	}

	if strings.TrimSpace(c.OutputFile) == "" {
		return fmt.Errorf("output file is required")
	}

	if c.MaxDepth <= 0 {
		return fmt.Errorf("max depth must be positive: %d", c.MaxDepth)
	}

	if c.MinSamplesSplit < 2 {
		return fmt.Errorf("min samples split must be >= 2: %d", c.MinSamplesSplit)
	}

	criterion := strings.ToLower(strings.TrimSpace(c.Criterion))
	if criterion != "entropy" {
		return fmt.Errorf("only entropy criterion supported, got: %s", c.Criterion)
	}

	// Validate optional feature info file if provided
	if strings.TrimSpace(c.FeatureInfoFile) != "" {
		if err := validateYAMLFile(c.FeatureInfoFile); err != nil {
			return fmt.Errorf("feature info file validation: %w", err)
		}
	}

	return nil
}

// Validate performs comprehensive validation for prediction configuration.
//
// Checks file existence and required parameters for prediction workflow.
// Returns first validation error encountered for faster feedback.
func (c *PredictionConfig) Validate() error {
	if err := validateInputFile(c.InputFile); err != nil {
		return fmt.Errorf("input file validation: %w", err)
	}

	if strings.TrimSpace(c.ModelFile) == "" {
		return fmt.Errorf("model file is required")
	}

	if err := validateModelFile(c.ModelFile); err != nil {
		return fmt.Errorf("model file validation: %w", err)
	}

	if strings.TrimSpace(c.OutputFile) == "" {
		return fmt.Errorf("output file is required")
	}

	return nil
}